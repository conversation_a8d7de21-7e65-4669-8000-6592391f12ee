#!/usr/bin/env python3
"""
调试配置解析的脚本
"""

import json

def test_json_parsing():
    """测试JSON解析功能"""
    
    # 测试正确的JSON字符串
    test_personas = '{"default": "你是一个有帮助的AI助手。", "programmer": "你是一个专业的程序员。", "teacher": "你是一个耐心的老师。"}'
    test_api_keys = '{"default": "sk-test-key-1", "backup": "sk-test-key-2"}'
    
    print("=== JSON解析测试 ===")
    
    try:
        personas = json.loads(test_personas)
        print(f"✅ personas解析成功: {personas}")
        print(f"   角色数量: {len(personas)}")
        print(f"   角色列表: {list(personas.keys())}")
    except json.JSONDecodeError as e:
        print(f"❌ personas解析失败: {e}")
    
    try:
        api_keys = json.loads(test_api_keys)
        print(f"✅ api_keys解析成功: {api_keys}")
        print(f"   密钥数量: {len(api_keys)}")
        print(f"   密钥列表: {list(api_keys.keys())}")
    except json.JSONDecodeError as e:
        print(f"❌ api_keys解析失败: {e}")

def simulate_plugin_config():
    """模拟插件配置解析"""
    
    print("\n=== 模拟插件配置解析 ===")
    
    # 模拟配置
    config = {
        "personas": '{"default": "你是一个有帮助的AI助手。", "programmer": "你是一个专业的程序员。", "teacher": "你是一个耐心的老师。"}',
        "api_keys": '{"default": "sk-test-key-1", "backup": "sk-test-key-2"}'
    }
    
    def parse_json_config(key: str, default_value: dict) -> dict:
        """模拟插件的JSON解析函数"""
        config_value = config.get(key)
        if not config_value:
            print(f"⚠️  配置项 {key} 为空，使用默认值")
            return default_value
        
        # 如果已经是字典类型，直接返回（向后兼容）
        if isinstance(config_value, dict):
            print(f"✅ 配置项 {key} 已是字典格式")
            return config_value
        
        # 如果是字符串，尝试解析JSON
        if isinstance(config_value, str):
            try:
                parsed = json.loads(config_value)
                if isinstance(parsed, dict):
                    print(f"✅ 配置项 {key} JSON解析成功")
                    return parsed
                else:
                    print(f"❌ 配置项 {key} 不是有效的JSON对象，使用默认值")
                    return default_value
            except json.JSONDecodeError as e:
                print(f"❌ 配置项 {key} JSON解析失败: {str(e)}，使用默认值")
                return default_value
        
        print(f"❌ 配置项 {key} 格式不正确，使用默认值")
        return default_value
    
    # 测试解析
    personas = parse_json_config("personas", {
        "default": "你是一个有帮助的AI助手。",
        "programmer": "你是一个专业的程序员，擅长解决编程问题和代码优化。",
        "teacher": "你是一个耐心的老师，善于用简单易懂的方式解释复杂概念。",
        "translator": "你是一个专业的翻译，能够准确翻译各种语言。"
    })
    
    api_keys = parse_json_config("api_keys", {})
    
    print(f"\n最终解析结果:")
    print(f"personas: {personas}")
    print(f"api_keys: {api_keys}")
    
    print(f"\n角色列表: {list(personas.keys())}")
    print(f"API密钥列表: {list(api_keys.keys())}")

def test_command_parsing():
    """测试命令解析"""
    
    print("\n=== 命令解析测试 ===")
    
    test_commands = [
        "personas",
        "persona programmer",
        "person teacher",
        "key backup",
        "keyls",
        "status",
        "help",
        "clear"
    ]
    
    for cmd in test_commands:
        print(f"\n测试命令: '{cmd}'")
        
        content = cmd.strip()
        
        if content.lower() == "personas":
            print("  ✅ 匹配: 列出角色")
        elif content.lower().startswith("persona ") or content.lower().startswith("person "):
            prefix_len = 8 if content.lower().startswith("persona ") else 7
            persona_name = content[prefix_len:].strip()
            print(f"  ✅ 匹配: 切换角色到 '{persona_name}'")
        elif content.lower() == "keyls":
            print("  ✅ 匹配: 列出API密钥")
        elif content.lower().startswith("key "):
            key_name = content[4:].strip()
            print(f"  ✅ 匹配: 切换API密钥到 '{key_name}'")
        elif content.lower() == "status":
            print("  ✅ 匹配: 显示状态")
        elif content.lower() == "help":
            print("  ✅ 匹配: 显示帮助")
        elif content.lower() == "clear":
            print("  ✅ 匹配: 清除上下文")
        else:
            print("  ➡️  普通聊天内容")

if __name__ == "__main__":
    test_json_parsing()
    simulate_plugin_config()
    test_command_parsing()
    
    print("\n=== 调试建议 ===")
    print("1. 检查astrbot日志中是否有 'DZMM插件: 已加载 X 个角色' 的信息")
    print("2. 确认配置界面中的JSON字符串格式是否正确")
    print("3. 使用 /dzmm status 命令查看当前配置状态")
    print("4. 如果角色列表仍然显示默认值，可能是配置没有正确传入插件")
